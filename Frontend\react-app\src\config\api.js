// API Configuration File
// TODO: Replace with your actual backend URL when ready

// Development/Local Backend URL
// export const API_BASE_URL = 'http://localhost:5000/api';

// Production Backend URL  
// export const API_BASE_URL = 'https://your-backend-domain.com/api';

// For now, using placeholder - Replace when backend is ready
export const API_BASE_URL = '/api';

// API Endpoints
export const API_ENDPOINTS = {
  // Questions
  QUESTIONS: '/questions',
  QUESTION_BY_ID: (id) => `/questions/${id}`,
  
  // Answers
  ANSWERS: '/answers',
  ANSWER_BY_ID: (id) => `/answers/${id}`,
  QUESTION_ANSWERS: (questionId) => `/questions/${questionId}/answers`,
  VOTE_ANSWER: (answerId) => `/answers/${answerId}/vote`,
  ACCEPT_ANSWER: (answerId) => `/answers/${answerId}/accept`,
  
  // Users & Auth
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  PROFILE: '/users/profile',
  
  // File Upload
  UPLOAD: '/upload',
  
  // Search
  SEARCH: '/search'
};

// HTTP Methods Helper
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH'
};

// Default Headers
export const getDefaultHeaders = (includeAuth = true) => {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  if (includeAuth) {
    // TODO: Get token from auth context/localStorage
    // const token = localStorage.getItem('authToken');
    // if (token) {
    //   headers.Authorization = `Bearer ${token}`;
    // }
  }
  
  return headers;
};

// API Helper Functions
export const apiCall = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions = {
    method: HTTP_METHODS.GET,
    headers: getDefaultHeaders(),
  };
  
  const finalOptions = { ...defaultOptions, ...options };
  
  try {
    const response = await fetch(url, finalOptions);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
};

// Specific API Functions - Ready to use when backend is implemented

// Questions API
export const questionsAPI = {
  // Get all questions
  getAll: () => apiCall(API_ENDPOINTS.QUESTIONS),
  
  // Get question by ID
  getById: (id) => apiCall(API_ENDPOINTS.QUESTION_BY_ID(id)),
  
  // Create new question
  create: (questionData) => apiCall(API_ENDPOINTS.QUESTIONS, {
    method: HTTP_METHODS.POST,
    body: JSON.stringify(questionData)
  }),
  
  // Update question
  update: (id, questionData) => apiCall(API_ENDPOINTS.QUESTION_BY_ID(id), {
    method: HTTP_METHODS.PUT,
    body: JSON.stringify(questionData)
  }),
  
  // Delete question
  delete: (id) => apiCall(API_ENDPOINTS.QUESTION_BY_ID(id), {
    method: HTTP_METHODS.DELETE
  })
};

// Answers API
export const answersAPI = {
  // Get answers for a question
  getByQuestionId: (questionId) => apiCall(API_ENDPOINTS.QUESTION_ANSWERS(questionId)),
  
  // Create new answer
  create: (questionId, answerData) => apiCall(API_ENDPOINTS.QUESTION_ANSWERS(questionId), {
    method: HTTP_METHODS.POST,
    body: JSON.stringify(answerData)
  }),
  
  // Vote on answer
  vote: (answerId, voteType) => apiCall(API_ENDPOINTS.VOTE_ANSWER(answerId), {
    method: HTTP_METHODS.POST,
    body: JSON.stringify({ voteType })
  }),
  
  // Accept answer
  accept: (answerId, questionId) => apiCall(API_ENDPOINTS.ACCEPT_ANSWER(answerId), {
    method: HTTP_METHODS.POST,
    body: JSON.stringify({ questionId })
  })
};

// Auth API
export const authAPI = {
  login: (credentials) => apiCall(API_ENDPOINTS.LOGIN, {
    method: HTTP_METHODS.POST,
    body: JSON.stringify(credentials)
  }),
  
  register: (userData) => apiCall(API_ENDPOINTS.REGISTER, {
    method: HTTP_METHODS.POST,
    body: JSON.stringify(userData)
  }),
  
  getProfile: () => apiCall(API_ENDPOINTS.PROFILE)
};
