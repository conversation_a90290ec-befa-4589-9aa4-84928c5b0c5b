import React, { createContext, useContext, useState } from 'react';

const QuestionContext = createContext();

export const useQuestion = () => {
  const context = useContext(QuestionContext);
  if (!context) {
    throw new Error('useQuestion must be used within a QuestionProvider');
  }
  return context;
};

export const QuestionProvider = ({ children }) => {
  const [questions, setQuestions] = useState([]);
  const [currentQuestionId, setCurrentQuestionId] = useState(null);

  const loadQuestions = (data) => {
    setQuestions(data);
    if (data.length > 0) {
      setCurrentQuestionId(data[0].question_id || data[0].id);
    }
  };

  const getCurrentQuestion = () => {
    return questions.find(q => q.question_id === currentQuestionId || q.id === currentQuestionId);
  };

  const addAnswer = (answerContent) => {
    const newAnswer = {
      id: Date.now(),
      content: answerContent,
      author: "current_user",
      votes: 0,
      isAccepted: false,
      createdAt: new Date().toISOString()
    };

    const index = questions.findIndex(q => (q.question_id || q.id) === currentQuestionId);
    if (index !== -1) {
      const updated = [...questions];
      updated[index].answers = [...(updated[index].answers || []), newAnswer];
      setQuestions(updated);
    }
  };

  const voteAnswer = (answerId, voteType) => {
    const updated = questions.map(q => {
      if ((q.question_id || q.id) !== currentQuestionId) return q;

      const answers = q.answers.map(a => {
        if (a.answer_id === answerId || a.id === answerId) {
          return {
            ...a,
            votes: (a.votes || 0) + (voteType === 'up' ? 1 : -1)
          };
        }
        return a;
      });

      return { ...q, answers };
    });

    setQuestions(updated);
  };

  const acceptAnswer = (answerId) => {
    const updated = questions.map(q => {
      if ((q.question_id || q.id) !== currentQuestionId) return q;

      const answers = q.answers.map(a => ({
        ...a,
        isAccepted: (a.answer_id === answerId || a.id === answerId)
      }));

      return { ...q, answers, accepted_answer_id: answerId };
    });

    setQuestions(updated);
  };

  const addComment = (answerId, commentContent) => {
    const newComment = {
      id: Date.now(),
      content: commentContent,
      author: "current_user",
      createdAt: new Date().toISOString()
    };

    const updated = questions.map(q => {
      if ((q.question_id || q.id) !== currentQuestionId) return q;

      const answers = q.answers.map(a => {
        if (a.answer_id === answerId || a.id === answerId) {
          const comments = [...(a.comments || []), newComment];
          return { ...a, comments };
        }
        return a;
      });

      return { ...q, answers };
    });

    setQuestions(updated);
  };

  const value = {
    questions,
    setQuestions,
    loadQuestions,
    currentQuestionId,
    setCurrentQuestionId,
    getCurrentQuestion,
    addAnswer,
    voteAnswer,
    acceptAnswer,
    addComment
  };

  return (
    <QuestionContext.Provider value={value}>
      {children}
    </QuestionContext.Provider>
  );
};
