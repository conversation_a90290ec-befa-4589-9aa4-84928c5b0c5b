import React, { createContext, useContext, useState, useEffect } from 'react';

const LoginContext = createContext();

export const useLogin = () => {
  const context = useContext(LoginContext);
  if (!context) {
    throw new Error('useLogin must be used within a LoginProvider');
  }
  return context;
};

export const LoginProvider = ({ children }) => {
  const [loginSuccessful, setLoginSuccessful] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check if user is already logged in (from localStorage)
  useEffect(() => {
    const savedUser = localStorage.getItem('stackit_user');
    const savedLoginStatus = localStorage.getItem('stackit_login_status');
    
    if (savedUser && savedLoginStatus === 'true') {
      setUser(JSON.parse(savedUser));
      setLoginSuccessful(true);
    }
    setLoading(false);
  }, []);

  // Login function
  const login = (userData) => {
    setUser(userData);
    setLoginSuccessful(true);
    
    // Save to localStorage
    localStorage.setItem('stackit_user', JSON.stringify(userData));
    localStorage.setItem('stackit_login_status', 'true');
  };

  // Logout function
  const logout = () => {
    setUser(null);
    setLoginSuccessful(false);
    
    // Clear localStorage
    localStorage.removeItem('stackit_user');
    localStorage.removeItem('stackit_login_status');
  };

  const value = {
    loginSuccessful,
    user,
    loading,
    login,
    logout
  };

  return (
    <LoginContext.Provider value={value}>
      {children}
    </LoginContext.Provider>
  );
};
