{"name": "@types/retry", "version": "0.12.0", "description": "TypeScript definitions for retry", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/krenor", "githubUsername": "krenor"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "dc8156aa3c27632d2585c45d8951c4a0f7ae9122bd17190d8973018556d174f3", "typeScriptVersion": "2.3"}