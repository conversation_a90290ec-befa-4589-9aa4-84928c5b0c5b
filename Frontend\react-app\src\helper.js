
const AZURE_FUNCTION_KEY = process.env.REACT_APP_AZURE_FUNCTION_KEY; // Replace with your actual Azure Function key

async function checkAlreadyRegistred(email) {
  console.log("Checking registration for email:", email);
  const azureUrl = `https://stackit-e3h5bqcqcyg9fnhz.centralindia-01.azurewebsites.net/api/fetchuserdata?email=${email}&code=${AZURE_FUNCTION_KEY}`;

  try {
    const response = await fetch(azureUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log("User data fetched:", data);

    if (data.success) {
      const { email, username, image_url } = data;
      return { email_id: email, username: username, image_url:image_url };
    } else {
      return { email_id: null, username: null, image_url: null };
    }
  } catch (error) {
    console.error("Error fetching user:", error);
    return { email_id: null, username: null, image_url: null };
  }
}

async function registerUser(payload) {
    const azureUrl = `ttps://stackit-e3h5bqcqcyg9fnhz.centralindia-01.azurewebsites.net/api/registeruser?code=${AZURE_FUNCTION_KEY}`;
    
    try {
        const response = await fetch(azureUrl, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            // "x-functions-key": AZURE_FUNCTION_KEY,
        },
        body: JSON.stringify(payload),
        });
    
        if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
        }
    
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error registering user:", error);
        return { success: false, message: error.message };
    }
    
}

const fetchQuestionsFromAPI = async (setQuestions) => {
  try {
    const response = await fetch("http://localhost:7071/api/fetchQuestions");
    if (!response.ok) {
      throw new Error(`Failed to fetch: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && Array.isArray(data.questions)) {
      // Convert datetime strings to Date if needed
      const parsedQuestions = data.questions.map((q) => ({
        ...q,
        createdAt: new Date(q.createdAt),
        updatedAt: new Date(q.updatedAt),
        answers: q.answers.map((a) => ({
          ...a,
          createdAt: new Date(a.createdAt),
          updatedAt: new Date(a.updatedAt),
          comments: a.comments || [],
        })),
      }));

      setQuestions(parsedQuestions);
    } else {
      console.warn("Invalid data format from fetchQuestions");
    }
  } catch (err) {
    console.error("Error fetching questions:", err.message);
  }
};

const uploadQuestionToAPI = async (question) => {
  console.log("Uploading question:", question);
  try {
    const response = await fetch(`https://stackit-e3h5bqcqcyg9fnhz.centralindia-01.azurewebsites.net/api/createquestion?code=${AZURE_FUNCTION_KEY}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // "x-functions-key": AZURE_FUNCTION_KEY,
      },
      body: JSON.stringify(question),
    });

    if (!response.ok) {
      throw new Error(`Failed to upload question: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error uploading question:", error);
    return { success: false, message: error.message };
  }
};


export { checkAlreadyRegistred,registerUser, fetchQuestionsFromAPI,uploadQuestionToAPI };