from datetime import datetime, UTC, timezone

from firebase.instance import db
import firebase_admin.firestore as firestore
import logging
import uuid
import firebase_admin
from firebase_admin import credentials, firestore


def serialize_firestore_data(data: dict) -> dict:
    for key, value in data.items():
        if isinstance(value, dict):
            data[key] = serialize_firestore_data(value)
        elif isinstance(value, list):
            data[key] = [serialize_firestore_data(v) if isinstance(v, dict) else (
                v.isoformat() if hasattr(v, 'isoformat') else v
            ) for v in value]
        elif hasattr(value, 'isoformat'):
            data[key] = value.isoformat()
    return data


def store_google_user(username: str, email: str, image_url: str, notifications: list = None) -> dict:
    email_id = email.strip().lower()
    user_ref = db.collection("user").document(email_id)
    existing_doc = user_ref.get()

    if existing_doc.exists:
        data = existing_doc.to_dict()
        return {
            "exists": True,
            "username": data.get("username"),
            "registrationData": serialize_firestore_data(data)
        }

    if notifications is None:
        notifications = []

    user_data = {
        "username": username,
        "email": email_id,
        "image_url": image_url,
        "notifications": notifications,
        "createdAt": datetime.now(UTC)
    }

    user_ref.set(user_data)
    return {"exists": False}


def fetch_user_by_email(email: str) -> dict:
    email_id = email.strip().lower()
    user_ref = db.collection("user").document(email_id)
    doc = user_ref.get()

    if not doc.exists:
        return {"success": False, "message": f"User with email '{email_id}' not found."}

    data = doc.to_dict()

    return serialize_firestore_data({
        "success": True,
        "email": email_id,
        "username": data.get("username", ""),
        "image_url": data.get("image_url", "")
    })


def delete_user_by_email(email: str) -> dict:
    email_id = email.strip().lower()
    user_ref = db.collection("user").document(email_id)

    if user_ref.get().exists:
        user_ref.delete()
        return {"success": True, "message": f"User '{email}' deleted."}
    return {"success": False, "message": f"User '{email}' does not exist."}


def fetch_notifications_by_email(email: str) -> dict:
    email_id = email.strip().lower()
    user_ref = db.collection("user").document(email_id)
    doc = user_ref.get()

    if doc.exists:
        user_data = doc.to_dict()
        return serialize_firestore_data({
            "exists": True,
            "email": email_id,
            "username": user_data.get("username"),
            "notification_count": len(user_data.get("notifications", [])),
            "notifications": user_data.get("notifications", [])
        })
    return {"exists": False, "message": f"User '{email_id}' not found."}


def add_notification(email: str, title: str, body: str, read=False) -> dict:
    email_id = email.strip().lower()
    user_ref = db.collection("user").document(email_id)
    doc = user_ref.get()

    if not doc.exists:
        return {"success": False, "message": f"User '{email}' not found."}

    new_notification = {
        "title": title,
        "body": body,
        "timestamp": datetime.now(UTC).isoformat(),
        "read": read
    }

    try:
        user_ref.update({"notifications": firestore.ArrayUnion([new_notification])})
        return {"success": True, "message": "Notification added.", "notification": new_notification}
    except Exception as e:
        return {"success": False, "message": f"Error adding notification: {str(e)}"}


def clear_notifications_by_email(email: str) -> dict:
    email_id = email.strip().lower()
    user_ref = db.collection("user").document(email_id)
    doc = user_ref.get()

    if not doc.exists():
        return {"success": False, "message": f"User '{email_id}' does not exist."}

    try:
        user_ref.update({"notifications": []})
        return {"success": True, "message": f"All notifications cleared for '{email_id}'."}
    except Exception as e:
        return {"success": False, "message": f"Error clearing notifications: {str(e)}"}


def delete_notification_by_index(email: str, index: int) -> dict:
    email_id = email.strip().lower()
    user_ref = db.collection("user").document(email_id)
    doc = user_ref.get()

    if not doc.exists():
        return {"success": False, "message": f"User '{email_id}' does not exist."}

    data = doc.to_dict()
    notifications = data.get("notifications", [])

    if index < 0 or index >= len(notifications):
        return {"success": False, "message": f"Invalid index {index}."}

    removed = notifications.pop(index)

    try:
        user_ref.update({"notifications": notifications})
        return serialize_firestore_data({
            "success": True,
            "message": f"Notification at index {index} deleted.",
            "removed": removed
        })
    except Exception as e:
        return {"success": False, "message": f"Failed to delete notification: {str(e)}"}


def create_question(title: str, body: str, tags: list, user_id: str) -> dict:
    question_id = str(uuid.uuid4())
    timestamp = datetime.now(timezone.utc).isoformat()

    question_doc = {
        "title": title,
        "body": body,
        "tags": tags,
        "number_of_answers": 0,
        "user_id": user_id,
        "createdAt": timestamp,
        "updatedAt": timestamp,
        "upvotes": 0,
        "answers": [],
        "accepted_answer_id": None
    }

    try:
        db.collection("questions").document(question_id).set(question_doc)
        return {
            "success": True,
            "question_id": question_id,
            "message": "Question successfully created.",
            "question": question_doc
        }
    except Exception as e:
        return {
            "success": False,
            "question_id": None,
            "message": f"Error: {str(e)}"
        }


def add_answer_to_question(question_id: str, body: str, user_id: str, accepted: bool = False, upvotes: int = 0) -> dict:
    answer_id = str(uuid.uuid4())
    timestamp = datetime.now(timezone.utc).isoformat()

    answer_data = {
        "answer_id": answer_id,
        "question_id": question_id,
        "body": body,
        "user_id": user_id,
        "createdAt": timestamp,
        "updatedAt": timestamp,
        "upvotes": upvotes,
        "accepted": accepted
    }

    try:
        question_ref = db.collection("questions").document(question_id)
        question_ref.update({
            "answers": firestore.ArrayUnion([answer_data]),
            "number_of_answers": firestore.Increment(1),
            "updatedAt": timestamp
        })

        if accepted:
            question_ref.update({"accepted_answer_id": answer_id})

        db.collection("answers").document(answer_id).set(answer_data)

        return {
            "success": True,
            "answer_id": answer_id,
            "message": "Answer successfully added.",
            "answer": answer_data
        }

    except Exception as e:
        return {
            "success": False,
            "answer_id": None,
            "message": f"Error adding answer: {str(e)}"
        }


def update_accepted_answer(question_id: str, answer_id: str) -> dict:
    try:
        question_ref = db.collection("questions").document(question_id)
        question_doc = question_ref.get()

        if not question_doc.exists:
            return {
                "success": False,
                "answer_id": None,
                "message": f"Question with ID '{question_id}' does not exist."
            }

        timestamp = datetime.now(timezone.utc).isoformat()

        
        question_ref.update({
            "accepted_answer_id": answer_id,
            "updatedAt": timestamp
        })

        data = question_doc.to_dict()
        updated_answers = []
        for answer in data.get("answers", []):
            answer["accepted"] = (answer["answer_id"] == answer_id)
            updated_answers.append(answer)

        question_ref.update({"answers": updated_answers})

        return serialize_firestore_data({
            "success": True,
            "answer_id": answer_id,
            "message": "Accepted answer updated.",
            "updatedAnswers": updated_answers
        })

    except Exception as e:
        return {
            "success": False,
            "answer_id": None,
            "message": f"Error: {str(e)}"
        }


def upvote_answer(question_id: str, answer_id: str) -> dict:
    try:
        question_ref = db.collection("questions").document(question_id)
        question_doc = question_ref.get()

        if not question_doc.exists:
            return {
                "success": False,
                "message": f" Question with ID '{question_id}' not found.",
                "answer_id": None
            }

        data = question_doc.to_dict()
        updated_answers = []
        found = False
        timestamp = datetime.now(timezone.utc).isoformat()

        for answer in data.get("answers", []):
            if answer["answer_id"] == answer_id:
                answer["upvotes"] += 1
                answer["updatedAt"] = timestamp
                found = True
            updated_answers.append(answer)

        if not found:
            return {
                "success": False,
                "message": f" Answer '{answer_id}' not found.",
                "answer_id": None
            }

        question_ref.update({
            "answers": updated_answers,
            "updatedAt": timestamp
        })

        answer_ref = db.collection("answers").document(answer_id)
        if answer_ref.get().exists:
            answer_ref.update({
                "upvotes": firestore.Increment(1),
                "updatedAt": timestamp
            })

        return {
            "success": True,
            "message": "Answer upvoted successfully.",
            "answer_id": answer_id
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"Error while upvoting: {str(e)}",
            "answer_id": None
        }


def fetch_all_questions() -> dict:
    try:
        questions_ref = db.collection("questions")
        questions = questions_ref.stream()

        all_questions = []
        for question in questions:
            data = serialize_firestore_data(question.to_dict())
            data["question_id"] = question.id
            all_questions.append(data)

        return {
            "success": True,
            "questions": all_questions
        }

    except Exception as e:
        logging.error(f"Error fetching questions: {str(e)}")
        return {
            "success": False,
            "message": f"Error fetching questions: {str(e)}"
        }