import { useState } from 'react';
import { useParams } from 'react-router-dom';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { useQuestion } from '../context/QuestionContext';

const QuestionDetail = () => {
  const { questionId } = useParams();
  const [answerText, setAnswerText] = useState('');
  const [commentText, setCommentText] = useState({});
  const [showCommentBox, setShowCommentBox] = useState({});
  const { questions, addAnswer, voteAnswer, acceptAnswer, addComment } = useQuestion();

  const questionData = questions.find((q) => q.id === questionId);

  if (!questionData) {
    return (
      <div className="bg-gray-50 font-poppins">
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="text-center py-16">
            <div className="mb-8">
              <svg className="w-24 h-24 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h2 className="text-3xl font-semibold text-gray-900 mb-4">Ready for Backend Integration</h2>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              This page is ready to display questions from your backend API.
              Connect your backend to see real questions and answers here.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 max-w-lg mx-auto">
              <h3 className="font-semibold text-blue-900 mb-2">Next Steps:</h3>
              <ul className="text-sm text-blue-800 text-left space-y-1">
                <li>• Set up your backend API</li>
                <li>• Update API_BASE_URL in config/api.js</li>
                <li>• Uncomment API calls in QuestionContext.js</li>
                <li>• Add authentication if needed</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleSubmitAnswer = async (e) => {
    e.preventDefault();
    if (!answerText.trim()) return;

    try {
      await addAnswer(questionId, answerText); // updated with questionId
      setAnswerText('');
      alert('Answer submitted successfully!');
    } catch (error) {
      console.error('Error submitting answer:', error);
      alert('Failed to submit answer. Please try again.');
    }
  };

  const handleVote = (answerId, voteType) => {
    voteAnswer(questionId, answerId, voteType);
  };

  const handleAcceptAnswer = (answerId) => {
    acceptAnswer(questionId, answerId);
  };

  const handleAddComment = async (answerId) => {
    const comment = commentText[answerId];
    if (!comment?.trim()) return;

    try {
      await addComment(questionId, answerId, comment);
      setCommentText(prev => ({ ...prev, [answerId]: '' }));
      setShowCommentBox(prev => ({ ...prev, [answerId]: false }));
    } catch (error) {
      console.error('Error adding comment:', error);
      alert('Failed to add comment. Please try again.');
    }
  };

  const toggleCommentBox = (answerId) => {
    setShowCommentBox(prev => ({ ...prev, [answerId]: !prev[answerId] }));
  };

  const handleCommentChange = (answerId, value) => {
    setCommentText(prev => ({ ...prev, [answerId]: value }));
  };

  return (
    <div className="bg-gray-50 font-poppins">
      <div className="max-w-4xl mx-auto px-6 py-8">

        <div className="text-sm text-gray-500 mb-4">
          <span>Questions</span>
          <span className="mx-2">/</span>
          <span>{questionData?.title?.substring(0, 50)}...</span>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h1 className="text-2xl font-semibold text-gray-900 mb-4 leading-relaxed">
            {questionData?.title}
          </h1>
          <div
            className="text-gray-700 mb-6 leading-relaxed prose max-w-none"
            dangerouslySetInnerHTML={{ __html: questionData?.description }}
          />
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div className="flex items-center flex-wrap gap-2">
              {questionData?.tags?.map((tag, index) => (
                <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium">
                  {tag}
                </span>
              ))}
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>Asked {questionData?.askedTime}</span>
              <span>•</span>
              <span>{questionData?.votes} votes</span>
              <span>•</span>
              <span>{questionData?.views} views</span>
            </div>
          </div>
        </div>

        {/* Answers Section */}
        {questionData?.answers?.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">
                Answers ({questionData.answers.length})
              </h2>
            </div>

            {questionData.answers.map((answer, index) => (
              <div key={answer.id} className={`${index > 0 ? 'border-t border-gray-200 pt-6 mt-6' : ''}`}>
                <div className="flex items-start space-x-4">
                  <div className="flex flex-col items-center space-y-2">
                    <button
                      onClick={() => handleVote(answer.id, 'up')}
                      className="p-1 hover:bg-gray-100 rounded transition-colors"
                    >
                      <svg className="w-6 h-6 text-gray-400 hover:text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                      </svg>
                    </button>
                    <span className="text-lg font-medium text-gray-700">{answer.votes}</span>
                    <button
                      onClick={() => handleVote(answer.id, 'down')}
                      className="p-1 hover:bg-gray-100 rounded transition-colors"
                    >
                      <svg className="w-6 h-6 text-gray-400 hover:text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                    {answer.isAccepted && (
                      <div className="mt-2">
                        <svg className="w-6 h-6 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                        </svg>
                      </div>
                    )}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <span className="font-medium text-gray-900">@{answer.author}</span>
                        <span className="text-sm text-gray-500">{answer.createdAt}</span>
                        {answer.isAccepted && (
                          <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full font-medium">
                            ✓ Accepted
                          </span>
                        )}
                      </div>
                      {!answer.isAccepted && (
                        <button
                          onClick={() => handleAcceptAnswer(answer.id)}
                          className="text-sm text-green-600 hover:text-green-800 font-medium transition-colors"
                        >
                          Accept Answer
                        </button>
                      )}
                    </div>

                    <div
                      className="text-gray-700 leading-relaxed mb-4 prose max-w-none"
                      dangerouslySetInnerHTML={{ __html: answer.content }}
                    />

                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => toggleCommentBox(answer.id)}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors"
                      >
                        Add Comment
                      </button>
                    </div>

                    {/* Comments */}
                    {answer.comments && answer.comments.length > 0 && (
                      <div className="mt-4 border-t border-gray-100 pt-4 space-y-3">
                        {answer.comments.map((comment) => (
                          <div key={comment.id} className="bg-gray-50 rounded-lg p-3">
                            <p className="text-sm text-gray-700 mb-1">{comment.content}</p>
                            <div className="text-xs text-gray-500">
                              @{comment.author} • {comment.createdAt}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Add Comment Box */}
                    {showCommentBox[answer.id] && (
                      <div className="mt-4 border-t border-gray-100 pt-4 flex space-x-3">
                        <input
                          type="text"
                          value={commentText[answer.id] || ''}
                          onChange={(e) => handleCommentChange(answer.id, e.target.value)}
                          placeholder="Add a comment..."
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') handleAddComment(answer.id);
                          }}
                        />
                        <button
                          onClick={() => handleAddComment(answer.id)}
                          className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700"
                        >
                          Post
                        </button>
                        <button
                          onClick={() => toggleCommentBox(answer.id)}
                          className="px-4 py-2 text-gray-500 text-sm rounded-lg hover:bg-gray-100"
                        >
                          Cancel
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Submit Answer */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Submit Your Answer</h3>
          <form onSubmit={handleSubmitAnswer}>
            <div className="mb-6">
              <ReactQuill
                theme="snow"
                value={answerText}
                onChange={setAnswerText}
                placeholder="Write your answer here..."
                modules={{
                  toolbar: [
                    ['bold', 'italic', 'underline', 'strike'],
                    ['blockquote', 'code-block'],
                    [{ header: [1, 2, false] }],
                    [{ list: 'ordered' }, { list: 'bullet' }],
                    [{ indent: '-1' }, { indent: '+1' }],
                    ['link', 'image'],
                    ['clean'],
                  ],
                }}
                formats={[
                  'header', 'bold', 'italic', 'underline', 'strike',
                  'blockquote', 'list', 'bullet', 'indent', 'link', 'image', 'code-block',
                ]}
                style={{ height: '200px', fontFamily: 'Poppins, sans-serif' }}
              />
              <div className="text-right mt-2">
                <span className="text-xs text-gray-500 font-poppins">Rich text</span>
              </div>
            </div>
            <div className="flex justify-end">
              <button
                type="submit"
                className="px-8 py-3 bg-orange-500 hover:bg-orange-600 text-white font-medium rounded-full transition-colors"
              >
                Submit
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default QuestionDetail;
