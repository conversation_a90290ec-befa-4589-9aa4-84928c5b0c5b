// src/LoginPage.jsx
import React from "react";
import { signInWithPopup } from "firebase/auth";
import {
  auth,
  provider
} from "../firebase";
import { useLogin } from "../context/LoginContext";

import { checkAlreadyRegistred,registerUser } from "../helper";

const LoginPage = () => {
  const { login } = useLogin();

  const handleLogin = async () => {
    try {
      const result = await signInWithPopup(auth, provider);
      const user = result.user;
      const email = user.email;

      // 1. Check if user already registered
      const userData = await checkAlreadyRegistred(email);
      console.log("User data:", userData);
      if (userData?.email_id) {
        alert("Welcome back, " + userData.username);
        const userCache = {
        email: userData.email,
        username: userData.username,
        photoURL: userData.image_url || "https://via.placeholder.com/150"
        };

        localStorage.setItem("user", JSON.stringify(userCache));

        // Update login state
        login(userCache);
        return;
      }

      // 2. If not registered, ask for username
      let username = "";
      while (!username) {
        username = prompt("Enter a username:");
        if (username === null) return; // User canceled
        username = username.trim();
      }

    //   email = req_body.get('email')
    //     profile_url = req_body.get('profile_url')
    //     username = req_body.get('username')
    const payload  ={
        email_id: email,
        username: username,
        image_url: user.photoURL || "https://via.placeholder.com/150"
      };

      // 3. Register user in Azure Function
      const response = await registerUser(payload);
      if (response.success) {
        alert("Registration successful! Welcome, " + username);
        const userCache = {
          email: email,
          username: username,
          photoURL: user.photoURL || "https://via.placeholder.com/150"
        };
        localStorage.setItem("user", JSON.stringify(userCache));

        // Update login state
        login(userCache);
      } else {
        alert("Registration failed: " + response.message);
      }


    
    } catch (error) {
      console.error("Google sign-in error", error);
      alert("Sign-in failed: " + error.message);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-blue-50 flex items-center justify-center p-4 font-poppins">
      {/* Background Pattern */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-orange-100 rounded-full opacity-20"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-100 rounded-full opacity-20"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-orange-100 to-blue-100 rounded-full opacity-10"></div>
      </div>

      {/* Login Card */}
      <div className="relative bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md border border-gray-100">
        {/* Logo Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl mb-4 shadow-lg">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to STACK<span className="text-orange-500">IT</span>
          </h1>
          <p className="text-gray-600 text-sm">
            Join our community of developers and get your questions answered
          </p>
        </div>

        {/* Login Form */}
        <div className="space-y-6">
          {/* Google Sign In Button */}
          <button
            onClick={handleLogin}
            className="w-full flex items-center justify-center px-6 py-4 bg-white border-2 border-gray-200 rounded-xl hover:border-gray-300 hover:shadow-md transition-all duration-200 group"
          >
            <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            <span className="text-gray-700 font-medium group-hover:text-gray-900 transition-colors">
              Continue with Google
            </span>
          </button>

          {/* Additional Info */}
          <div className="text-center">
            <p className="text-sm text-gray-600">
              Sign in securely with your Google account to access all features
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-xs text-gray-500">
            By signing in, you agree to our{" "}
            <a href="#" className="text-orange-500 hover:text-orange-600 transition-colors">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="#" className="text-orange-500 hover:text-orange-600 transition-colors">
              Privacy Policy
            </a>
          </p>
        </div>

        {/* Features */}
        <div className="mt-8 grid grid-cols-3 gap-4 text-center">
          <div className="p-3">
            <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-xs text-gray-600">Ask Questions</p>
          </div>
          <div className="p-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-xs text-gray-600">Get Answers</p>
          </div>
          <div className="p-3">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <p className="text-xs text-gray-600">Join Community</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
