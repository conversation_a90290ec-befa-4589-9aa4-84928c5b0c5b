

import { Link, useLocation } from 'react-router-dom';
import { useLogin } from '../context/LoginContext';

const Header = ({disableLogin}) => {
  const location = useLocation();
  const { user, logout } = useLogin();

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-3">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        {/* Logo */}
        <div className="flex items-center">
          <Link to="/" className="text-xl font-semibold font-poppins text-gray-900 hover:text-orange-500 transition-colors">
            STACK
            <span className="text-orange-500 text-xs align-super ml-0.5">IT</span>
          </Link>
        </div>

        {/* Navigation Links */}
        <div className="flex items-center space-x-6">
          {user ? (
            // Logged in - show working links
            <>
              <Link
                to="/questions"
                className={`px-4 py-2 rounded-lg font-poppins text-sm transition-colors ${
                  location.pathname === '/questions'
                    ? 'bg-orange-500 text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                Questions
              </Link>
              <Link
                to="/add-question"
                className={`px-4 py-2 rounded-lg font-poppins text-sm transition-colors ${
                  location.pathname === '/add-question'
                    ? 'bg-orange-500 text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                Ask Question
              </Link>
            </>
          ) : (
            // Not logged in - show disabled buttons
            <>
              <button
                onClick={() => alert('Please login first to access Questions')}
                className="px-4 py-2 rounded-lg font-poppins text-sm text-gray-400 cursor-not-allowed"
              >
                Questions
              </button>
              <button
                onClick={() => alert('Please login first to Ask Questions')}
                className="px-4 py-2 rounded-lg font-poppins text-sm text-gray-400 cursor-not-allowed"
              >
                Ask Question
              </button>
            </>
          )}
        </div>

        {/* Search Bar */}
        <div className="flex-1 max-w-2xl mx-8">
          <div className="relative">
            <input
              type="text"
              placeholder="Search for questions or answers"
              className="w-full px-4 py-2.5 bg-gray-50 border border-gray-200 rounded-full text-sm font-poppins placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
            />
            <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-orange-500 hover:text-orange-600 transition-colors">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          </div>
        </div>

        {/* Right Side Icons */}
        <div className="flex items-center space-x-4">
          {/* Notification Bell - Only show when logged in */}
          {user && (
            <button className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 15.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v4.159c0 .538-.214 1.055-.595 1.436L4 17h16z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.73 21a2 2 0 01-3.46 0" />
              </svg>
              {/* Notification Badge */}
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
            </button>
          )}

          {/* Profile Icon */}
          {!disableLogin && (
            <>
              {user ? (
                // Logged in user - show profile with logout
                <div className="flex items-center space-x-3">
                  <img
                    src={user.photoURL || "https://via.placeholder.com/32"}
                    alt={user.username || user.name}
                    className="w-8 h-8 rounded-full border-2 border-gray-200"
                  />
                  <span className="text-sm font-medium text-gray-700 font-poppins">
                    {user.username || user.name}
                  </span>
                  <button
                    onClick={logout}
                    className="px-3 py-1 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors font-poppins"
                  >
                    Logout
                  </button>
                </div>
              ) : (
                // Not logged in - show profile icon only
                <Link
                  to="/login"
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </Link>
              )}
            </>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
