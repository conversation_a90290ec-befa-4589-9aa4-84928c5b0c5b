@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for HTML content rendering */
.prose {
  @apply text-gray-700 leading-relaxed;
}

.prose h1, .prose h2, .prose h3 {
  @apply font-semibold text-gray-900 mb-3;
}

.prose h1 { @apply text-2xl; }
.prose h2 { @apply text-xl; }
.prose h3 { @apply text-lg; }

.prose p {
  @apply mb-4;
}

.prose ul, .prose ol {
  @apply mb-4 pl-6;
}

.prose ul {
  @apply list-disc;
}

.prose ol {
  @apply list-decimal;
}

.prose li {
  @apply mb-2;
}

.prose pre {
  @apply bg-gray-100 p-4 rounded-lg overflow-x-auto mb-4 text-sm;
}

.prose code {
  @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono;
}

.prose pre code {
  @apply bg-transparent p-0;
}

.prose strong {
  @apply font-semibold;
}

.prose blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-4;
}

body {
  margin: 0;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom Quill Editor Styles */
.ql-toolbar {
  border: none !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 8px 12px !important;
  background: transparent !important;
}

.ql-container {
  border: none !important;
  font-family: 'Poppins', sans-serif !important;
  background: transparent !important;
}

.ql-editor {
  padding: 12px 16px !important;
  min-height: 120px !important;
  font-family: 'Poppins', sans-serif !important;
  color: #374151 !important;
  background: transparent !important;
}

.ql-editor.ql-blank::before {
  color: #9ca3af !important;
  font-style: normal !important;
  font-family: 'Poppins', sans-serif !important;
}

.ql-toolbar .ql-formats {
  margin-right: 12px !important;
}

.ql-toolbar button {
  padding: 4px 6px !important;
  margin: 0 1px !important;
  border-radius: 4px !important;
}

.ql-toolbar button:hover {
  color: #f97316 !important;
  background: rgba(249, 115, 22, 0.1) !important;
}

.ql-toolbar button.ql-active {
  color: #f97316 !important;
  background: rgba(249, 115, 22, 0.1) !important;
}

.ql-snow .ql-stroke {
  stroke: #6b7280 !important;
}

.ql-snow .ql-stroke:hover {
  stroke: #f97316 !important;
}

.ql-snow .ql-fill {
  fill: #6b7280 !important;
}

.ql-snow .ql-fill:hover {
  fill: #f97316 !important;
}
