import React from 'react';
import { useLogin } from '../context/LoginContext';

const ProtectedRoute = ({ children }) => {
  const { user } = useLogin();

  if (!user) {
    return (
      <div className="bg-gray-50 font-poppins">
        <div className="max-w-4xl mx-auto px-6 py-16">
          <div className="text-center">
            <div className="mb-8">
              <svg className="w-24 h-24 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h2 className="text-3xl font-semibold text-gray-900 mb-4">Login Required</h2>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              You need to be logged in to access this feature. Please click the profile icon in the top right corner to sign in.
            </p>
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 max-w-lg mx-auto">
              <h3 className="font-semibold text-orange-900 mb-2">How to Login:</h3>
              <ol className="text-sm text-orange-800 text-left space-y-1">
                <li>1. Click the profile icon (👤) in the top right corner</li>
                <li>2. Sign in with your Google account</li>
                <li>3. Return here to access all features</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return children;
};

export default ProtectedRoute;
