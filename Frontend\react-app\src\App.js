
import './App.css';
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Header from './components/Header';
import ProtectedRoute from './components/ProtectedRoute';
import Home from './pages/Home';
import AddQuestion from './pages/AddQuestion';
import LoginPage from './pages/login';
import QuestionDetail from './pages/QuestionDetail';
import { QuestionProvider } from './context/QuestionContext';
import { LoginProvider, useLogin } from './context/LoginContext';



function App() {
  return (
    <LoginProvider>
      <div className="App">
        <AppContent />
      </div>
    </LoginProvider>
  );
}

const AppContent = () => {
  const { loading } = useLogin();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600 font-poppins">Loading...</p>
        </div>
      </div>
    );
  }

  return <MainBrowser />;
};



const MainBrowser = () => {
  return (
    <QuestionProvider>
      <BrowserRouter>
        <div className="min-h-screen bg-gray-50">
          <Header />
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/home" element={<Home />} />
            <Route path="/questions" element={
              <ProtectedRoute>
                <QuestionDetail />
              </ProtectedRoute>
            } />
            <Route path="/questions/:id" element={
              <ProtectedRoute>
                <QuestionDetail />
              </ProtectedRoute>
            } />
            <Route path="/add-question" element={
              <ProtectedRoute>
                <AddQuestion />
              </ProtectedRoute>
            } />
            <Route path="/login" element={<LoginPage />} />
          </Routes>
        </div>
      </BrowserRouter>
    </QuestionProvider>
  );
}

export default App;
