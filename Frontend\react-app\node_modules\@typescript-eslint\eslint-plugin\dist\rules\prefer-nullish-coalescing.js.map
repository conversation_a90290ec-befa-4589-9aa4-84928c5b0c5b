{"version": 3, "file": "prefer-nullish-coalescing.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-nullish-coalescing.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2E;AAC3E,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAuBhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,2BAA2B;IACjC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,2EAA2E;YAC7E,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,mBAAmB,EACjB,4GAA4G;YAC9G,wBAAwB,EACtB,4GAA4G;YAC9G,cAAc,EAAE,4CAA4C;YAC5D,iBAAiB,EACf,kGAAkG;SACrG;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,sDAAsD,EAAE;wBACtD,IAAI,EAAE,SAAS;qBAChB;oBACD,sBAAsB,EAAE;wBACtB,IAAI,EAAE,SAAS;qBAChB;oBACD,6BAA6B,EAAE;wBAC7B,IAAI,EAAE,SAAS;qBAChB;oBACD,gBAAgB,EAAE;wBAChB,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC3B,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC5B,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC3B,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;yBAC5B;qBACF;oBACD,kBAAkB,EAAE;wBAClB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,sDAAsD,EAAE,KAAK;YAC7D,sBAAsB,EAAE,IAAI;YAC5B,kBAAkB,EAAE,IAAI;YACxB,6BAA6B,EAAE,IAAI;YACnC,gBAAgB,EAAE;gBAChB,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,KAAK;gBACb,MAAM,EAAE,KAAK;aACd;SACF;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,sDAAsD,EACtD,sBAAsB,EACtB,6BAA6B,EAC7B,gBAAgB,EAChB,kBAAkB,GACnB,EACF;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,eAAe,GAAG,cAAc,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACpE,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,kBAAkB,GAAG,OAAO,CAAC,6BAA6B,CAC9D,eAAe,EACf,kBAAkB,CACnB,CAAC;QAEF,IACE,CAAC,kBAAkB;YACnB,sDAAsD,KAAK,IAAI,EAC/D;YACA,OAAO,CAAC,MAAM,CAAC;gBACb,GAAG,EAAE;oBACH,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;oBAC7B,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;iBAC5B;gBACD,SAAS,EAAE,mBAAmB;aAC/B,CAAC,CAAC;SACJ;QAED,OAAO;YACL,qBAAqB,CAAC,IAAoC;gBACxD,IAAI,kBAAkB,EAAE;oBACtB,OAAO;iBACR;gBAED,IAAI,QAAiD,CAAC;gBACtD,IAAI,yBAAyB,GAAoB,EAAE,CAAC;gBACpD,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE;oBACtD,yBAAyB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC9D,IACE,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;wBAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;wBAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;wBAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK,EAC5B;wBACA,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;qBAC/B;iBACF;qBAAM,IACL,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBACnD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACvD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EACxD;oBACA,yBAAyB,GAAG;wBAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;wBACnB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;wBACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;wBACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;qBACtB,CAAC;oBACF,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;wBAC/B,IACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,EAClC;4BACA,QAAQ,GAAG,KAAK,CAAC;yBAClB;6BAAM,IACL,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC;4BACnC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;4BACvC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,EACpC;4BACA,QAAQ,GAAG,IAAI,CAAC;yBACjB;qBACF;yBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;wBACtC,IACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,EAClC;4BACA,QAAQ,GAAG,KAAK,CAAC;yBAClB;6BAAM,IACL,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC;4BACnC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;4BACvC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,EACpC;4BACA,QAAQ,GAAG,IAAI,CAAC;yBACjB;qBACF;iBACF;gBAED,IAAI,CAAC,QAAQ,EAAE;oBACb,OAAO;iBACR;gBAED,IAAI,UAAqC,CAAC;gBAC1C,IAAI,iBAAiB,GAAG,KAAK,CAAC;gBAC9B,IAAI,YAAY,GAAG,KAAK,CAAC;gBAEzB,0EAA0E;gBAC1E,KAAK,MAAM,QAAQ,IAAI,yBAAyB,EAAE;oBAChD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;wBAChC,YAAY,GAAG,IAAI,CAAC;qBACrB;yBAAM,IAAI,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE;wBAC/C,iBAAiB,GAAG,IAAI,CAAC;qBAC1B;yBAAM,IACL,CAAC,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,IAAI,CAAC;wBACzC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,EAC3C;wBACA,UAAU,GAAG,QAAQ,CAAC;qBACvB;yBAAM,IACL,CAAC,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,IAAI,CAAC;wBACzC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,EAC1C;wBACA,UAAU,GAAG,QAAQ,CAAC;qBACvB;yBAAM;wBACL,OAAO;qBACR;iBACF;gBAED,IAAI,CAAC,UAAU,EAAE;oBACf,OAAO;iBACR;gBAED,MAAM,SAAS,GAAG,CAAC,GAAY,EAAE;oBAC/B,2EAA2E;oBAC3E,IAAI,iBAAiB,KAAK,YAAY,EAAE;wBACtC,OAAO,iBAAiB,CAAC;qBAC1B;oBAED,iEAAiE;oBACjE,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE;wBAC1C,OAAO,IAAI,CAAC;qBACb;oBAED,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBACpE,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;oBAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBAEtC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;wBACrD,OAAO,KAAK,CAAC;qBACd;oBAED,MAAM,WAAW,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAEtD,uEAAuE;oBACvE,IAAI,iBAAiB,IAAI,CAAC,WAAW,EAAE;wBACrC,OAAO,IAAI,CAAC;qBACb;oBAED,MAAM,gBAAgB,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBAEhE,qEAAqE;oBACrE,OAAO,YAAY,IAAI,CAAC,gBAAgB,CAAC;gBAC3C,CAAC,CAAC,EAAE,CAAC;gBAEL,IAAI,SAAS,EAAE;oBACb,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,0BAA0B;wBACrC,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,gBAAgB;gCAC3B,GAAG,CAAC,KAAyB;oCAC3B,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GACjB,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,IAAI;wCACrC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC;wCACnC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;oCACxC,OAAO,KAAK,CAAC,WAAW,CACtB,IAAI,EACJ,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CACtB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EACb,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CACd,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAC3B,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EACd,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACf,EAAE,CACJ,CAAC;gCACJ,CAAC;6BACF;yBACF;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC;YAED,oCAAoC,CAClC,IAAgC;gBAEhC,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC9D,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACpD,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC;gBACtE,IAAI,CAAC,SAAS,EAAE;oBACd,OAAO;iBACR;gBAED,IAAI,sBAAsB,KAAK,IAAI,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;oBAC9D,OAAO;iBACR;gBAED,MAAM,cAAc,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC;gBACtD,IAAI,6BAA6B,KAAK,IAAI,IAAI,cAAc,EAAE;oBAC5D,OAAO;iBACR;gBAED,MAAM,cAAc,GAAG;oBACrB,gBAAiB,CAAC,MAAM,IAAI,EAAE,CAAC,SAAS,CAAC,MAAM;oBAC/C,gBAAiB,CAAC,OAAO,IAAI,EAAE,CAAC,SAAS,CAAC,cAAc;oBACxD,gBAAiB,CAAC,MAAM,IAAI,EAAE,CAAC,SAAS,CAAC,MAAM;oBAC/C,gBAAiB,CAAC,MAAM,IAAI,EAAE,CAAC,SAAS,CAAC,MAAM;iBAChD;qBACE,MAAM,CAAC,CAAC,IAAI,EAAkB,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC;qBACpD,MAAM,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;gBAClD,IACG,IAAmC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAClD,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,cAAc,CAAC,CACzC,EACD;oBACA,OAAO;iBACR;gBAED,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CACpC,UAAU,CAAC,aAAa,CACtB,IAAI,CAAC,IAAI,EACT,KAAK,CAAC,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;oBACzC,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,CAChC,EACD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAC3D,CAAC;gBAEF,QAAQ,CAAC,CAAC,GAAG,CACX,KAAyB;oBAEzB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;wBACxD,kFAAkF;wBAClF,IACE,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;4BACnD,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EACzC;4BACA,MAAM,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;yBACpD;6BAAM;4BACL,MAAM,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;yBAC9C;wBACD,MAAM,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;qBAC9C;oBACD,MAAM,KAAK,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;gBAChD,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,cAAc;oBACpB,SAAS,EAAE,qBAAqB;oBAChC,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,gBAAgB;4BAC3B,GAAG;yBACJ;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,iBAAiB,CAAC,IAAmB;IAC5C,MAAM,OAAO,GAAG,IAAI,GAAG,CAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;IACtD,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;IAC1B,OAAO,OAAO,EAAE;QACd,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAErB,IACE,CAAC,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB;YACpD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;YAChD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW;YAC3C,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY;YAC5C,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;YACjD,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EACzB;YACA,OAAO,IAAI,CAAC;SACb;QAED,IACE;YACE,sBAAc,CAAC,uBAAuB;YACtC,sBAAc,CAAC,kBAAkB;SAClC,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EACxB;YACA;;;;eAIG;YACH,OAAO,KAAK,CAAC;SACd;QAED,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;KAC1B;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,wBAAwB,CAAC,IAAgC;IAChE,MAAM,IAAI,GAAG,IAAI,GAAG,EAA6B,CAAC;IAClD,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACnD,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE;QAC3B,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACrB,SAAS;SACV;QACD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAElB,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;YAChE,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,EAAE;gBAC7B,OAAO,IAAI,CAAC;aACb;iBAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,EAAE;gBACpC,sEAAsE;gBACtE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;aACzD;SACF;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}