import { useState } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { uploadQuestionToAPI } from '../helper';

const AddQuestion = () => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    tags: '',
    image: null
  });
  const [successMsg, setSuccessMsg] = useState('');


  const handleInputChange = (e) => {
    const { name, value, type, files } = e.target;
    if (type === 'file') {
      setFormData(prev => ({
        ...prev,
        [name]: files[0]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSuccessMsg('');

      // TODO: Replace with your actual backend URL
      // const API_BASE_URL = 'http://localhost:5000/api'; // Add your backend URL here

      const data = new FormData();
      data.append('title', formData.title);
      data.append('body', formData.description);
      data.append('tags', formData.tags);
      if (formData.image) {
        data.append('image', formData.image);
      }
      data.append("user_id","ari_archit_" );//JSON.parse(localStorage.getItem("user")).email);
      // console.log(data)

      const toSenddata = {
        title: formData.title,
        body: formData.description,
        tags: formData.tags,
        user_id: "ari_archit_"
      };
      const response = await uploadQuestionToAPI(toSenddata);

      // TODO: Replace '/api/upload' with actual backend endpoint
      // const response = await fetch(`${API_BASE_URL}/questions`, {
      

      //   method: 'POST',
      //   body: data
      // });

      if (response.success) {
        setSuccessMsg('Question added successfully!');
        setFormData({
          title: '',
          description: '',
          tags: '',
          image: null
        });
      } else {
        alert('Failed to add question: ' + response.message);
      }
    }

  return (
    <div className="min-h-screen bg-gray-50 font-poppins">
      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Page Title */}
        <h1 className="text-3xl font-semibold text-gray-900 mb-8">
          Add Question
        </h1>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6" encType="multipart/form-data">

          {/* Title Field */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              Title
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="w-full px-4 py-3 bg-gray-100 border-0 rounded-lg focus:bg-white focus:ring-2 focus:ring-orange-500 focus:outline-none transition-all duration-200 font-poppins"
              placeholder=""
            />
          </div>

          {/* Description Field */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <div className="bg-gray-100 rounded-lg overflow-hidden border-0 focus-within:bg-white focus-within:ring-2 focus-within:ring-orange-500 transition-all duration-200">
              <ReactQuill
                theme="snow"
                value={formData.description}
                onChange={(value) => setFormData(prev => ({ ...prev, description: value }))}
                placeholder="Describe your question in detail..."
                modules={{
                  toolbar: [
                    ['bold', 'italic', 'link'],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    ['blockquote', 'code-block'],
                    ['image']
                  ],
                }}
                formats={[
                  'bold', 'italic', 'link', 'list', 'bullet',
                  'blockquote', 'code-block', 'image'
                ]}
                style={{
                  height: '200px',
                  fontFamily: 'Poppins, sans-serif'
                }}
              />
            </div>
            <div className="text-right mt-2">
              <span className="text-xs text-gray-500 font-poppins">HTML Rich Text</span>
            </div>
          </div>

          {/* Tags Field */}
          <div>
            <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
              Tags
            </label>
            <input
              type="text"
              id="tags"
              name="tags"
              value={formData.tags}
              onChange={handleInputChange}
              className="w-full px-4 py-3 bg-gray-100 border-0 rounded-lg focus:bg-white focus:ring-2 focus:ring-orange-500 focus:outline-none transition-all duration-200 font-poppins"
              placeholder="User comma to separate tags"
            />
          </div>

          {/* Submit Button */}
          <div className="flex flex-col items-end pt-4 gap-2">
            <button
              type="submit"
              className="px-8 py-3 bg-orange-500 hover:bg-orange-600 text-white font-medium rounded-full transition-colors duration-200 font-poppins"
            >
              Submit
            </button>
            {successMsg && (
              <span className="text-green-600 font-poppins text-sm">{successMsg}</span>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddQuestion;
