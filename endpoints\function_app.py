import azure.functions as func
import datetime
import json
import logging
from firebase.crud import *

app = func.FunctionApp()


@app.route(route="fetchUserData", auth_level=func.AuthLevel.FUNCTION)
def fetch_user_data(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('HTTP function triggered to fetch user data.')
    try:
        if req.method == "OPTIONS":
            return func.HttpResponse(
                "",
                status_code=204,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )
        
        email = req.params.get('email')
        if not email:
            return func.HttpResponse(
                "Email parameter is required.",
                status_code=400,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )

        user_data = fetch_user_by_email(email)
        # send the email to the database to fetch user data

        return func.HttpResponse(
            json.dumps(user_data),
            status_code=200,
            mimetype="application/json",
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )
        
    except Exception as e:
        return func.HttpResponse(
            f"Error processing request: {str(e)}",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

@app.route(route="registerUser", auth_level=func.AuthLevel.FUNCTION)
def register_user(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('HTTP function triggered to register user.')
    try:
        if req.method == "OPTIONS":
            return func.HttpResponse(
                "",
                status_code=204,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )
        
        req_body = req.get_json()

        email = req_body.get('email')
        profile_url = req_body.get('profile_url')
        username = req_body.get('username')


        if not email or not profile_url or not username:
            return func.HttpResponse(
                "Make sure the payload contains email, profile_url, username parameter is required.",
                status_code=400,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )
        
        check = store_google_user(username,email,profile_url,[])

        if not check["exists"]:
            return func.HttpResponse(
                "Successfully registered user",
                status_code=409,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )
        
    except Exception as e:
        return func.HttpResponse(
            f"Error processing request: {str(e)}",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

# Notification Related Function

@app.route(route="addNotification", auth_level=func.AuthLevel.FUNCTION)
def add_notifi(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('HTTP function triggered to add notification.')
    try:
        if req.method == "OPTIONS":
            return func.HttpResponse(
                "",
                status_code=204,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )
        
        req_body = req.get_json()

        email = req_body.get('email')
        title = req_body.get('title')   
        body = req_body.get('body')
        # sender = req_body.get('sender')

        if not email or not title:
            return func.HttpResponse(
                "Make sure the payload contains email and message parameters.",
                status_code=400,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )


        status = add_notification(email,title,body)
        logging.info(f"Notification status: {status}")
        if not status["success"]:
            return func.HttpResponse(
                status["message"],
                status_code=404,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )
        



        return func.HttpResponse(
            json.dumps({"message": "Notification added successfully"}),
            status_code=200,
            mimetype="application/json",
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )
        
    except Exception as e:
        return func.HttpResponse(
            f"Error processing request: {str(e)}",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

@app.route(route="fetchNotifications", auth_level=func.AuthLevel.FUNCTION)
def fetch_notifi(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('HTTP function triggered to fetch notifications.')
    try:
        if req.method == "OPTIONS":
            return func.HttpResponse(
                "",
                status_code=204,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )
        
        email = req.params.get('email')
        if not email:
            return func.HttpResponse(
                "Email parameter is required.",
                status_code=400,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )

        notifications = fetch_notifications_by_email(email)

        return func.HttpResponse(
            json.dumps(notifications),
            status_code=200,
            mimetype="application/json",
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )
        
    except Exception as e:
        return func.HttpResponse(
            f"Error processing request: {str(e)}",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )


@app.route(route="removeNotification", auth_level=func.AuthLevel.FUNCTION)
def remove_notifi(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('HTTP function triggered to remove notification.')
    try:
        if req.method == "OPTIONS":
            return func.HttpResponse(
                "",
                status_code=204,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "DELETE, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )
        
        req_body = req.get_json()

        email = req_body.get('email')
        index = req_body.get('index')

        if not email or not index:
            return func.HttpResponse(
                "Make sure the payload contains email and notification_id parameters.",
                status_code=400,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "DELETE, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )

        status = delete_notification_by_index(email,index)

        if not status["success"]:
            return func.HttpResponse(
                status["message"],
                status_code=404,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "DELETE, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )

        return func.HttpResponse(
            json.dumps({"message": "Notification removed successfully"}),
            status_code=200,
            mimetype="application/json",
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )
        
    except Exception as e:
        return func.HttpResponse(
            f"Error processing request: {str(e)}",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "DELETE, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

@app.route(route="checkHealth", auth_level=func.AuthLevel.FUNCTION)
def checkHealth(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('HTTP function triggered to check health.')
    try:
        if req.method == "OPTIONS":
            return func.HttpResponse(
                "",
                status_code=204,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )
        return func.HttpResponse(
            json.dumps({"status": "healthy", "timestamp": datetime.datetime.utcnow().isoformat()}),
            status_code=200,
            mimetype="application/json",
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )
        
    except Exception as e:
        return func.HttpResponse(
            f"Error processing request: {str(e)}",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )


@app.route(route="createQuestion", auth_level=func.AuthLevel.FUNCTION)
def create_question_api(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('HTTP function triggered to create a question.')

    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
        "Content-Type": "application/json"
    }

    if req.method == "OPTIONS":
        return func.HttpResponse(
            "",
            status_code=204,
            headers=cors_headers,
        )

    try:
        req_body = req.get_json()

        title = req_body.get("title")
        body = req_body.get("body")
        tags = req_body.get("tags")
        user_id = req_body.get("user_id")
        logging.info(f"Received request to create question with title: {title}, user_id: {user_id}")

        if not all([title, body, tags, user_id]):
            return func.HttpResponse(
                json.dumps({"error": "Missing required fields: title, body, tags, or user_id."}),
                status_code=400,
                headers=cors_headers,
            )
        logging.info(f"Creating question with title: {title}, body: {body}, tags: {tags}, user_id: {user_id}")
        result = create_question(title, body, tags, user_id)

        return func.HttpResponse(
            json.dumps(result),
            status_code=201 if result.get("success") else 500,
            headers=cors_headers,
        )

    except Exception as e:
        logging.exception("Exception in create_question_api")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            status_code=500,
            headers=cors_headers,
        )
        


@app.route(route="addAnswerToQuestion", auth_level=func.AuthLevel.FUNCTION)
def add_answer_to_question_api(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("HTTP function triggered to add an answer to a question.")

    if req.method == "OPTIONS":
        return func.HttpResponse(
            "",
            status_code=204,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    try:
        req_body = req.get_json()

        question_id = req_body.get("question_id")
        body = req_body.get("body")
        user_id = req_body.get("user_id")
        accepted = req_body.get("accepted", False)
        upvotes = req_body.get("upvotes", 0)

        if not all([question_id, body, user_id]):
            return func.HttpResponse(
                "Missing required fields: question_id, body, user_id",
                status_code=400,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )

        result = add_answer_to_question(question_id, body, user_id, accepted, upvotes)

        return func.HttpResponse(
            body=str(result),
            status_code=201 if result["success"] else 500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
                "Content-Type": "application/json",
            },
        )

    except Exception as e:
        logging.exception("Error in add_answer_to_question_api")
        return func.HttpResponse(
            f"Error processing request: {str(e)}",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )


@app.route(route="updateAcceptedAnswer", auth_level=func.AuthLevel.FUNCTION)
def update_accepted_answer_api(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("HTTP function triggered to update accepted answer.")

    if req.method == "OPTIONS":
        return func.HttpResponse(
            "",
            status_code=204,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    try:
        req_body = req.get_json()

        question_id = req_body.get("question_id")
        answer_id = req_body.get("answer_id")

        if not question_id or not answer_id:
            return func.HttpResponse(
                body=json.dumps({
                    "success": False,
                    "message": "Missing required fields: question_id and answer_id",
                    "answer_id": None
                }),
                status_code=400,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                    "Content-Type": "application/json",
                },
            )

        result = update_accepted_answer(question_id, answer_id)

        status_code = 200 if result["success"] else 404
        return func.HttpResponse(
            body=json.dumps(result),
            status_code=status_code,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
                "Content-Type": "application/json",
            },
        )

    except Exception as e:
        logging.exception("Error in updateAcceptedAnswer API")
        return func.HttpResponse(
            body=json.dumps({
                "success": False,
                "message": f"Internal server error: {str(e)}",
                "answer_id": None
            }),
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
                "Content-Type": "application/json",
            },
        )

        
@app.route(route="upvoteAnswer", auth_level=func.AuthLevel.FUNCTION)
def upvote_answer_api(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("HTTP function triggered to upvote an answer.")

    if req.method == "OPTIONS":
        return func.HttpResponse(
            "",
            status_code=204,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    try:
        req_body = req.get_json()
        question_id = req_body.get("question_id")
        answer_id = req_body.get("answer_id")

        if not question_id or not answer_id:
            return func.HttpResponse(
                "Missing required fields: question_id and answer_id.",
                status_code=400,
                headers={
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "POST, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type",
                },
            )

        result = upvote_answer(question_id, answer_id)

        return func.HttpResponse(
            body=str(result),
            status_code=200 if result["success"] else 404,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
                "Content-Type": "application/json"
            }
        )

    except Exception as e:
        logging.exception("Error in upvoteAnswer API")
        return func.HttpResponse(
            f"Error processing request: {str(e)}",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

@app.route(route="fetchQuestions", auth_level=func.AuthLevel.FUNCTION)
def fetch_questions_api(req: func.HttpRequest) -> func.HttpResponse:
    logging.info("HTTP function triggered to fetch questions.")

    if req.method == "OPTIONS":
        return func.HttpResponse(
            "",
            status_code=204,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    try:
        questions = fetch_all_questions()

        return func.HttpResponse(
            body=json.dumps(questions),
            status_code=200,
            mimetype="application/json",
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except Exception as e:
        logging.exception("Error in fetchQuestions API")
        return func.HttpResponse(
            f"Error processing request: {str(e)}",
            status_code=500,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )